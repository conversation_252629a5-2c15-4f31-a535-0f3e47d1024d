#include <cassert>

#include <string>
#include "property.h"

int main(int argc, char* argv[]) {
    Property<std::string> firstname("<PERSON>");
    Property<std::string> lastname("<PERSON>");
    Property<int> age(argc);
    Property<std::string> fullname;
    fullname.setBinding([&]() {
        // TODO_REMOVE: 字符串拼接现代化 - 用std::format替换字符串拼接
        // 现代化方案: return std::format("{} {} age: {}", firstname.value(), lastname.value(), age.value());
        // 收益: 更好的性能、类型安全、格式化灵活性
        // 要求: C++20 std::format支持或{fmt}库
        return firstname.value() + " "
               + lastname.value() + " age: "
               + std::to_string(age.value());
    });
    std::cout << fullname.value() << std::endl;
    // TODO_REMOVE: 字符串拼接现代化 - 用std::format替换
    // 现代化方案: assert(fullname.value() == std::format("<PERSON> age: {}", argc));
    assert(fullname.value() == "<PERSON> age: " + std::to_string(argc));

    firstname = "Emma";
    std::cout << fullname.value() << std::endl;
    assert(fullname.value() == "Emma Smith age: " + std::to_string(argc));

    age.setValue(age.value() + 1);

    std::cout << fullname.value() << std::endl;
    // TODO_REMOVE: 字符串拼接现代化 - 用std::format替换
    // 现代化方案: assert(fullname.value() == std::format("Emma Smith age: {}", argc + 1));
    assert(fullname.value() == "Emma Smith age: " + std::to_string(argc + 1));
}
